#!/usr/bin/env python3
"""
Predictor de captchas usando modelo entrenado personalizado
"""

import os
import json
import numpy as np
import cv2
import tensorflow as tf
from tensorflow import keras
import argparse
import sys

class CaptchaPredictor:
    """Predictor de captchas usando modelo entrenado"""
    
    def __init__(self, model_dir="trained_model"):
        """Inicializa el predictor"""
        self.model_dir = model_dir
        self.model_path = os.path.join(model_dir, 'captcha_model.h5')
        self.metadata_path = os.path.join(model_dir, 'metadata.json')
        
        self.load_model()
    
    def load_model(self):
        """Carga el modelo y metadatos"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"Modelo no encontrado: {self.model_path}")
        
        if not os.path.exists(self.metadata_path):
            raise FileNotFoundError(f"Metadatos no encontrados: {self.metadata_path}")
        
        # Cargar metadatos
        with open(self.metadata_path, 'r', encoding='utf-8') as f:
            self.metadata = json.load(f)
        
        self.characters = self.metadata['characters']
        self.char_to_num = self.metadata['char_to_num']
        self.num_to_char = {int(k): v for k, v in self.metadata['num_to_char'].items()}
        self.img_width = self.metadata['img_width']
        self.img_height = self.metadata['img_height']
        self.max_length = self.metadata['max_length']
        
        # Cargar modelo
        self.model = keras.models.load_model(self.model_path)
        
        print(f"✅ Modelo cargado: {len(self.characters)} caracteres")
    
    def preprocess_image(self, image_path):
        """Preprocesa una imagen para predicción"""
        try:
            # Leer imagen
            if isinstance(image_path, str):
                image = cv2.imread(image_path)
            else:
                # Asumir que es bytes
                nparr = np.frombuffer(image_path, np.uint8)
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                return None
            
            # Convertir a escala de grises
            if len(image.shape) == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Redimensionar
            image = cv2.resize(image, (self.img_width, self.img_height))
            
            # Normalizar
            image = image.astype(np.float32) / 255.0
            
            # Expandir dimensiones
            image = np.expand_dims(image, axis=-1)
            image = np.expand_dims(image, axis=0)  # Batch dimension
            
            return image
            
        except Exception as e:
            print(f"❌ Error procesando imagen: {e}")
            return None
    
    def decode_prediction(self, prediction):
        """Decodifica una predicción a texto"""
        # Obtener índices con mayor probabilidad
        pred_indices = np.argmax(prediction[0], axis=-1)
        
        decoded = []
        for idx in pred_indices:
            if idx < len(self.characters):
                decoded.append(self.num_to_char[idx])
        
        # Limpiar resultado
        result = ''.join(decoded).strip()
        
        # Remover caracteres de padding
        result = result.replace(' ', '')
        
        return result
    
    def predict_from_file(self, image_path):
        """Predice captcha desde archivo"""
        try:
            # Preprocesar imagen
            processed_image = self.preprocess_image(image_path)
            if processed_image is None:
                return None
            
            # Hacer predicción
            prediction = self.model.predict(processed_image, verbose=0)
            
            # Decodificar resultado
            result = self.decode_prediction(prediction)
            
            return result if len(result) >= 4 else None
            
        except Exception as e:
            print(f"❌ Error en predicción: {e}")
            return None
    
    def predict_from_bytes(self, image_bytes):
        """Predice captcha desde bytes"""
        try:
            # Preprocesar imagen
            processed_image = self.preprocess_image(image_bytes)
            if processed_image is None:
                return None
            
            # Hacer predicción
            prediction = self.model.predict(processed_image, verbose=0)
            
            # Decodificar resultado
            result = self.decode_prediction(prediction)
            
            return result if len(result) >= 4 else None
            
        except Exception as e:
            print(f"❌ Error en predicción: {e}")
            return None
    
    def batch_predict(self, image_paths):
        """Predice múltiples captchas"""
        results = []
        
        for image_path in image_paths:
            result = self.predict_from_file(image_path)
            results.append({
                'file': os.path.basename(image_path),
                'prediction': result,
                'success': result is not None
            })
        
        return results
    
    def test_model(self, test_dir="captcha_dataset/images", num_samples=10):
        """Prueba el modelo con imágenes de muestra"""
        print(f"🧪 PROBANDO MODELO")
        print("=" * 40)
        
        if not os.path.exists(test_dir):
            print(f"❌ Directorio de prueba no encontrado: {test_dir}")
            return
        
        # Obtener imágenes de muestra
        image_files = [f for f in os.listdir(test_dir) if f.endswith('.png')]
        
        if not image_files:
            print("❌ No se encontraron imágenes de prueba")
            return
        
        # Seleccionar muestra aleatoria
        import random
        sample_files = random.sample(image_files, min(num_samples, len(image_files)))
        
        successful = 0
        total = len(sample_files)
        
        for i, filename in enumerate(sample_files, 1):
            image_path = os.path.join(test_dir, filename)
            
            print(f"\n🔍 Prueba {i}/{total}: {filename}")
            
            import time
            start_time = time.time()
            result = self.predict_from_file(image_path)
            end_time = time.time()
            
            duration = round((end_time - start_time) * 1000, 2)
            
            if result:
                print(f"✅ Predicción: {result} ({duration}ms)")
                successful += 1
            else:
                print(f"❌ Sin predicción ({duration}ms)")
        
        accuracy = successful / total
        print(f"\n📊 RESULTADOS:")
        print(f"   Éxitos: {successful}/{total}")
        print(f"   Precisión: {accuracy:.1%}")
        print(f"   Tiempo promedio: {sum(range(total))/total if total > 0 else 0:.1f}ms")

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Predictor de captchas SAT personalizado')
    parser.add_argument('image_path', nargs='?', help='Ruta a la imagen del captcha')
    parser.add_argument('--model-dir', default='trained_model', help='Directorio del modelo')
    parser.add_argument('--test', action='store_true', help='Ejecutar pruebas')
    parser.add_argument('--verbose', '-v', action='store_true', help='Modo verbose')
    
    args = parser.parse_args()
    
    try:
        predictor = CaptchaPredictor(args.model_dir)
        
        if args.test:
            predictor.test_model()
            return
        
        if not args.image_path:
            print("Error: Se requiere la ruta de la imagen", file=sys.stderr)
            parser.print_help()
            sys.exit(1)
        
        if not os.path.exists(args.image_path):
            print("Error: Archivo no encontrado", file=sys.stderr)
            sys.exit(1)
        
        result = predictor.predict_from_file(args.image_path)
        
        if result:
            print(result)  # Solo el resultado para captura por otros scripts
            if args.verbose:
                print(f"Captcha resuelto: {result}", file=sys.stderr)
            sys.exit(0)
        else:
            if args.verbose:
                print("No se pudo resolver el captcha", file=sys.stderr)
            sys.exit(1)
    
    except FileNotFoundError as e:
        print(f"Error: {e}", file=sys.stderr)
        print("💡 Primero entrena un modelo con train_captcha_model.py", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
