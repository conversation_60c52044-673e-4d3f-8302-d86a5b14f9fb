#!/usr/bin/env python3
"""
Recolector de datos de captchas del SAT
Automatiza la recolección de captchas para entrenamiento
"""

import os
import time
import json
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import hashlib

class CaptchaDataCollector:
    """Recolector automático de datos de captchas del SAT"""
    
    def __init__(self, output_dir="captcha_dataset"):
        """Inicializa el recolector"""
        self.output_dir = output_dir
        self.images_dir = os.path.join(output_dir, "images")
        self.labels_file = os.path.join(output_dir, "labels.json")
        
        # Crear directorios
        os.makedirs(self.images_dir, exist_ok=True)
        
        # Cargar etiquetas existentes
        self.labels = self.load_labels()
        
        # Configurar Selenium
        self.setup_driver()
    
    def setup_driver(self):
        """Configura el driver de Selenium"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        # chrome_options.add_argument("--headless")  # Comentado para ver el proceso
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.wait = WebDriverWait(self.driver, 10)
    
    def load_labels(self):
        """Carga las etiquetas existentes"""
        if os.path.exists(self.labels_file):
            with open(self.labels_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def save_labels(self):
        """Guarda las etiquetas"""
        with open(self.labels_file, 'w', encoding='utf-8') as f:
            json.dump(self.labels, f, indent=2, ensure_ascii=False)
    
    def get_image_hash(self, image_bytes):
        """Genera hash único para la imagen"""
        return hashlib.md5(image_bytes).hexdigest()

    def explore_page_for_captcha(self):
        """Explora la página para encontrar elementos de captcha"""
        print("🔍 Explorando página para encontrar captcha...")

        try:
            # Buscar todos los elementos img
            img_elements = self.driver.find_elements(By.TAG_NAME, "img")
            print(f"📊 Encontrados {len(img_elements)} elementos img")

            for i, img in enumerate(img_elements):
                src = img.get_attribute('src') or ''
                id_attr = img.get_attribute('id') or ''
                class_attr = img.get_attribute('class') or ''
                alt_attr = img.get_attribute('alt') or ''

                print(f"IMG {i+1}:")
                print(f"   src: {src[:80]}...")
                print(f"   id: {id_attr}")
                print(f"   class: {class_attr}")
                print(f"   alt: {alt_attr}")
                print()

            # Buscar elementos con texto relacionado a captcha
            captcha_texts = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'captcha') or contains(text(), 'Captcha') or contains(text(), 'CAPTCHA')]")
            if captcha_texts:
                print(f"📝 Encontrados {len(captcha_texts)} elementos con texto 'captcha'")
                for i, element in enumerate(captcha_texts[:3]):
                    print(f"   {i+1}: {element.text[:50]}...")

            # Buscar inputs relacionados
            inputs = self.driver.find_elements(By.XPATH, "//input[contains(@id, 'captcha') or contains(@name, 'captcha') or contains(@placeholder, 'captcha')]")
            if inputs:
                print(f"📝 Encontrados {len(inputs)} inputs relacionados con captcha")
                for i, inp in enumerate(inputs):
                    print(f"   Input {i+1}: id='{inp.get_attribute('id')}', name='{inp.get_attribute('name')}'")

        except Exception as e:
            print(f"❌ Error explorando página: {e}")
    
    def collect_captcha_from_sat(self):
        """Recolecta un captcha del sitio del SAT"""
        try:
            # Ir al sitio correcto del SAT para verificación de CFDIs
            print("🌐 Navegando al sitio del SAT...")
            self.driver.get("https://verificacfdi.facturaelectronica.sat.gob.mx/default.aspx")

            # Esperar a que cargue la página
            time.sleep(5)

            # Buscar el captcha - probar diferentes selectores
            captcha_element = None
            captcha_selectors = [
                "img[src*='captcha']",
                "img[src*='Captcha']",
                "img[id*='captcha']",
                "img[id*='Captcha']",
                "#BtnCaptcha",
                "#imgCaptcha",
                ".captcha img",
                "img[alt*='captcha']",
                "img[alt*='Captcha']"
            ]

            for selector in captcha_selectors:
                try:
                    captcha_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if captcha_element:
                        print(f"✅ Captcha encontrado con selector: {selector}")
                        break
                except:
                    continue

            if not captcha_element:
                print("❌ No se encontró el captcha con ningún selector")
                self.explore_page_for_captcha()
                return None

            # Obtener la imagen del captcha
            captcha_url = captcha_element.get_attribute('src')
            if not captcha_url:
                print("❌ No se pudo obtener URL del captcha")
                return None

            print(f"🔗 URL del captcha: {captcha_url[:100]}...")

            # Si la URL es relativa, hacerla absoluta
            if captcha_url.startswith('/'):
                captcha_url = "https://verificacfdi.facturaelectronica.sat.gob.mx" + captcha_url
            elif not captcha_url.startswith('http'):
                captcha_url = "https://verificacfdi.facturaelectronica.sat.gob.mx/" + captcha_url

            # Descargar imagen con headers apropiados
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://verificacfdi.facturaelectronica.sat.gob.mx/default.aspx'
            }

            response = requests.get(captcha_url, headers=headers, timeout=10)
            if response.status_code != 200:
                print(f"❌ Error descargando captcha: {response.status_code}")
                return None

            image_bytes = response.content

            # Verificar que sea una imagen válida
            if len(image_bytes) < 100:
                print("❌ Imagen muy pequeña, probablemente no es un captcha")
                return None

            image_hash = self.get_image_hash(image_bytes)

            # Verificar si ya tenemos esta imagen
            if image_hash in self.labels:
                print("⚠️  Imagen ya existe en el dataset")
                return None

            # Guardar imagen
            image_filename = f"captcha_{image_hash}.png"
            image_path = os.path.join(self.images_dir, image_filename)

            with open(image_path, "wb") as f:
                f.write(image_bytes)

            print(f"📸 Imagen guardada: {image_filename}")
            print(f"📏 Tamaño: {len(image_bytes)} bytes")

            return {
                'hash': image_hash,
                'filename': image_filename,
                'path': image_path,
                'size': len(image_bytes)
            }

        except Exception as e:
            print(f"❌ Error recolectando captcha: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def manual_labeling_session(self, num_captchas=50):
        """Sesión de etiquetado manual de captchas"""
        print(f"🏷️  INICIANDO SESIÓN DE ETIQUETADO MANUAL")
        print(f"🎯 Objetivo: {num_captchas} captchas")
        print("=" * 60)
        
        collected = 0
        skipped = 0
        
        while collected < num_captchas:
            print(f"\n📊 Progreso: {collected}/{num_captchas} (Omitidos: {skipped})")
            
            # Recolectar captcha
            captcha_info = self.collect_captcha_from_sat()
            
            if not captcha_info:
                skipped += 1
                print("⏭️  Saltando al siguiente...")
                time.sleep(2)
                continue
            
            # Mostrar la imagen al usuario
            print(f"\n🖼️  Imagen: {captcha_info['filename']}")
            print("👀 Mira la imagen del captcha en el navegador")
            
            # Pedir al usuario que ingrese la respuesta
            while True:
                answer = input("✏️  Ingresa el texto del captcha (o 'skip' para saltar, 'quit' para salir): ").strip()
                
                if answer.lower() == 'quit':
                    print("🛑 Sesión terminada por el usuario")
                    self.save_labels()
                    return collected
                
                if answer.lower() == 'skip':
                    print("⏭️  Saltando esta imagen...")
                    # Eliminar la imagen
                    os.remove(captcha_info['path'])
                    skipped += 1
                    break
                
                if len(answer) >= 4 and answer.replace(' ', '').isalnum():
                    # Guardar etiqueta
                    self.labels[captcha_info['hash']] = {
                        'filename': captcha_info['filename'],
                        'answer': answer.upper(),
                        'timestamp': time.time(),
                        'size': captcha_info['size']
                    }
                    
                    print(f"✅ Etiqueta guardada: {answer.upper()}")
                    collected += 1
                    self.save_labels()
                    break
                else:
                    print("❌ Respuesta inválida. Debe tener al menos 4 caracteres alfanuméricos.")
            
            # Pausa entre captchas
            time.sleep(1)
        
        print(f"\n🎉 ¡Sesión completada! Recolectados: {collected} captchas")
        return collected
    
    def auto_collect_session(self, num_captchas=100):
        """Sesión de recolección automática (solo imágenes, sin etiquetas)"""
        print(f"🤖 INICIANDO RECOLECCIÓN AUTOMÁTICA")
        print(f"🎯 Objetivo: {num_captchas} imágenes")
        print("=" * 60)
        
        collected = 0
        skipped = 0
        
        while collected < num_captchas:
            print(f"\n📊 Progreso: {collected}/{num_captchas} (Omitidos: {skipped})")
            
            captcha_info = self.collect_captcha_from_sat()
            
            if captcha_info:
                # Marcar como no etiquetado
                self.labels[captcha_info['hash']] = {
                    'filename': captcha_info['filename'],
                    'answer': None,  # Sin etiquetar
                    'timestamp': time.time(),
                    'size': captcha_info['size'],
                    'labeled': False
                }
                
                collected += 1
                print(f"✅ Imagen recolectada: {captcha_info['filename']}")
                
                if collected % 10 == 0:
                    self.save_labels()
            else:
                skipped += 1
            
            # Pausa entre recolecciones
            time.sleep(2)
        
        self.save_labels()
        print(f"\n🎉 ¡Recolección completada! Total: {collected} imágenes")
        return collected
    
    def import_existing_captchas(self):
        """Importa captchas existentes del directorio actual"""
        print("📂 Importando captchas existentes...")

        # Buscar archivos de captcha en el directorio actual
        import glob
        current_dir = os.getcwd()
        captcha_patterns = [
            'captcha_debug_*.png',
            'captcha*.png',
            '*captcha*.png'
        ]

        imported = 0
        for pattern in captcha_patterns:
            files = glob.glob(os.path.join(current_dir, pattern))
            for file_path in files:
                try:
                    # Leer imagen
                    with open(file_path, 'rb') as f:
                        image_bytes = f.read()

                    image_hash = self.get_image_hash(image_bytes)

                    # Verificar si ya existe
                    if image_hash in self.labels:
                        continue

                    # Copiar a dataset
                    filename = f"captcha_{image_hash}.png"
                    dest_path = os.path.join(self.images_dir, filename)

                    with open(dest_path, 'wb') as f:
                        f.write(image_bytes)

                    # Agregar a labels sin etiquetar
                    self.labels[image_hash] = {
                        'filename': filename,
                        'answer': None,
                        'timestamp': time.time(),
                        'size': len(image_bytes),
                        'labeled': False,
                        'source': 'imported'
                    }

                    imported += 1
                    print(f"   ✅ Importado: {os.path.basename(file_path)} -> {filename}")

                except Exception as e:
                    print(f"   ❌ Error importando {file_path}: {e}")

        if imported > 0:
            self.save_labels()
            print(f"🎉 Importadas {imported} imágenes de captcha")
        else:
            print("⚠️  No se encontraron captchas para importar")

        return imported

    def label_existing_images(self):
        """Etiqueta imágenes existentes sin etiquetar"""
        unlabeled = [hash_id for hash_id, info in self.labels.items()
                    if not info.get('answer')]

        if not unlabeled:
            print("✅ Todas las imágenes ya están etiquetadas")
            return 0

        print(f"🏷️  ETIQUETANDO IMÁGENES EXISTENTES")
        print(f"🎯 Imágenes sin etiquetar: {len(unlabeled)}")
        print("=" * 60)

        labeled = 0

        for i, hash_id in enumerate(unlabeled):
            info = self.labels[hash_id]
            image_path = os.path.join(self.images_dir, info['filename'])

            if not os.path.exists(image_path):
                continue

            print(f"\n📊 Progreso: {i+1}/{len(unlabeled)}")
            print(f"🖼️  Imagen: {info['filename']}")
            print(f"💡 Abre la imagen en: {image_path}")

            # Pedir etiqueta
            while True:
                answer = input("✏️  Ingresa el texto del captcha (o 'skip' para saltar, 'quit' para salir): ").strip()

                if answer.lower() == 'quit':
                    print("🛑 Etiquetado terminado por el usuario")
                    self.save_labels()
                    return labeled

                if answer.lower() == 'skip':
                    print("⏭️  Saltando esta imagen...")
                    break

                if len(answer) >= 4 and answer.replace(' ', '').isalnum():
                    # Actualizar etiqueta
                    self.labels[hash_id]['answer'] = answer.upper()
                    self.labels[hash_id]['labeled'] = True
                    self.labels[hash_id]['label_timestamp'] = time.time()

                    print(f"✅ Etiqueta guardada: {answer.upper()}")
                    labeled += 1
                    self.save_labels()
                    break
                else:
                    print("❌ Respuesta inválida. Debe tener al menos 4 caracteres alfanuméricos.")

        print(f"\n🎉 ¡Etiquetado completado! Etiquetadas: {labeled} imágenes")
        return labeled

    def show_dataset_stats(self):
        """Muestra estadísticas del dataset"""
        total_images = len(self.labels)
        labeled_images = sum(1 for label in self.labels.values() if label.get('answer'))
        unlabeled_images = total_images - labeled_images

        print("\n📊 ESTADÍSTICAS DEL DATASET")
        print("=" * 40)
        print(f"📁 Total de imágenes: {total_images}")
        print(f"🏷️  Imágenes etiquetadas: {labeled_images}")
        print(f"❓ Imágenes sin etiquetar: {unlabeled_images}")

        if labeled_images > 0:
            print(f"📈 Progreso de etiquetado: {(labeled_images/total_images)*100:.1f}%")

            # Mostrar distribución de longitudes
            lengths = {}
            for label in self.labels.values():
                if label.get('answer'):
                    length = len(label['answer'])
                    lengths[length] = lengths.get(length, 0) + 1

            print("\n📏 Distribución por longitud:")
            for length, count in sorted(lengths.items()):
                print(f"   {length} caracteres: {count} imágenes")

        # Mostrar fuentes de datos
        sources = {}
        for label in self.labels.values():
            source = label.get('source', 'collected')
            sources[source] = sources.get(source, 0) + 1

        if len(sources) > 1:
            print("\n📊 Fuentes de datos:")
            for source, count in sources.items():
                print(f"   {source}: {count} imágenes")
    
    def close(self):
        """Cierra el driver"""
        if hasattr(self, 'driver'):
            self.driver.quit()

def main():
    """Función principal"""
    collector = CaptchaDataCollector()

    try:
        while True:
            print("\n🎯 RECOLECTOR DE DATOS DE CAPTCHAS SAT")
            print("=" * 50)
            collector.show_dataset_stats()
            print("\nOpciones:")
            print("1. 📂 Importar captchas existentes del directorio")
            print("2. 🏷️  Etiquetar imágenes existentes")
            print("3. 🌐 Sesión de etiquetado manual (50 captchas nuevos)")
            print("4. 🤖 Recolección automática (100 imágenes)")
            print("5. 📊 Ver estadísticas del dataset")
            print("6. 🚪 Salir")

            choice = input("\nSelecciona una opción (1-6): ").strip()

            if choice == '1':
                collector.import_existing_captchas()
            elif choice == '2':
                collector.label_existing_images()
            elif choice == '3':
                collector.manual_labeling_session(50)
            elif choice == '4':
                collector.auto_collect_session(100)
            elif choice == '5':
                collector.show_dataset_stats()
            elif choice == '6':
                print("👋 ¡Hasta luego!")
                break
            else:
                print("❌ Opción inválida")

    except KeyboardInterrupt:
        print("\n🛑 Interrumpido por el usuario")
    finally:
        collector.close()

if __name__ == "__main__":
    main()
