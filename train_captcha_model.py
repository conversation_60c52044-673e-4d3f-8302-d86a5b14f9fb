#!/usr/bin/env python3
"""
Entrenador de modelo de captchas personalizado para SAT
Basado en CNN + CTC Loss para secuencias de texto
"""

import os
import json
import numpy as np
import cv2
from PIL import Image
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import string
import random

class CaptchaModelTrainer:
    """Entrenador de modelo de captchas personalizado"""
    
    def __init__(self, dataset_dir="captcha_dataset", model_dir="trained_model"):
        """Inicializa el entrenador"""
        self.dataset_dir = dataset_dir
        self.model_dir = model_dir
        self.images_dir = os.path.join(dataset_dir, "images")
        self.labels_file = os.path.join(dataset_dir, "labels.json")
        
        # Parámetros del modelo
        self.img_width = 160
        self.img_height = 60
        self.max_length = 6  # Longitud máxima del captcha
        
        # Crear directorio del modelo
        os.makedirs(model_dir, exist_ok=True)
        
        # Cargar datos
        self.load_dataset()
        self.prepare_character_mapping()
    
    def load_dataset(self):
        """Carga el dataset desde los archivos"""
        print("📂 Cargando dataset...")
        
        if not os.path.exists(self.labels_file):
            raise FileNotFoundError(f"Archivo de etiquetas no encontrado: {self.labels_file}")
        
        with open(self.labels_file, 'r', encoding='utf-8') as f:
            all_labels = json.load(f)
        
        # Filtrar solo las imágenes etiquetadas
        self.labels = {k: v for k, v in all_labels.items() 
                      if v.get('answer') and len(v['answer']) >= 4}
        
        print(f"✅ Dataset cargado: {len(self.labels)} imágenes etiquetadas")
        
        if len(self.labels) < 100:
            print("⚠️  ADVERTENCIA: Necesitas al menos 100 imágenes etiquetadas para un buen entrenamiento")
            print("   Recomendado: 500+ imágenes para resultados óptimos")
    
    def prepare_character_mapping(self):
        """Prepara el mapeo de caracteres"""
        # Obtener todos los caracteres únicos del dataset
        all_chars = set()
        for label_info in self.labels.values():
            all_chars.update(label_info['answer'])
        
        # Agregar caracteres comunes de captchas numéricos
        all_chars.update('0123456789')
        
        # Crear mapeo
        self.characters = sorted(list(all_chars))
        self.char_to_num = {char: idx for idx, char in enumerate(self.characters)}
        self.num_to_char = {idx: char for idx, char in enumerate(self.characters)}
        
        print(f"🔤 Caracteres detectados: {''.join(self.characters)}")
        print(f"📊 Total de caracteres únicos: {len(self.characters)}")
    
    def preprocess_image(self, image_path):
        """Preprocesa una imagen para el modelo"""
        try:
            # Leer imagen
            image = cv2.imread(image_path)
            if image is None:
                return None
            
            # Convertir a escala de grises
            if len(image.shape) == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Redimensionar
            image = cv2.resize(image, (self.img_width, self.img_height))
            
            # Normalizar
            image = image.astype(np.float32) / 255.0
            
            # Expandir dimensiones
            image = np.expand_dims(image, axis=-1)
            
            return image
            
        except Exception as e:
            print(f"❌ Error procesando imagen {image_path}: {e}")
            return None
    
    def encode_label(self, label):
        """Codifica una etiqueta de texto a números"""
        encoded = []
        for char in label:
            if char in self.char_to_num:
                encoded.append(self.char_to_num[char])
        
        # Padding hasta max_length
        while len(encoded) < self.max_length:
            encoded.append(len(self.characters))  # Token de padding
        
        return encoded[:self.max_length]
    
    def decode_prediction(self, prediction):
        """Decodifica una predicción a texto"""
        decoded = []
        for idx in prediction:
            if idx < len(self.characters):
                decoded.append(self.num_to_char[idx])
        
        return ''.join(decoded).strip()
    
    def prepare_data(self):
        """Prepara los datos para entrenamiento"""
        print("🔄 Preparando datos para entrenamiento...")
        
        images = []
        labels = []
        
        for hash_id, label_info in self.labels.items():
            image_path = os.path.join(self.images_dir, label_info['filename'])
            
            if not os.path.exists(image_path):
                continue
            
            # Preprocesar imagen
            image = self.preprocess_image(image_path)
            if image is None:
                continue
            
            # Codificar etiqueta
            encoded_label = self.encode_label(label_info['answer'])
            
            images.append(image)
            labels.append(encoded_label)
        
        self.images = np.array(images)
        labels_array = np.array(labels)

        # Convertir etiquetas a formato de múltiples salidas
        self.labels = {}
        for i in range(self.max_length):
            self.labels[f'char_{i}'] = labels_array[:, i]

        print(f"✅ Datos preparados: {len(self.images)} muestras")
        print(f"📏 Forma de imágenes: {self.images.shape}")
        label_shapes = [f'char_{i}: {self.labels[f"char_{i}"].shape}' for i in range(self.max_length)]
        print(f"📏 Forma de etiquetas: {label_shapes}")

        # Dividir en entrenamiento y validación
        self.X_train, self.X_val, y_train_temp, y_val_temp = train_test_split(
            self.images, labels_array, test_size=0.2, random_state=42
        )

        # Convertir y_train y y_val a formato de múltiples salidas
        self.y_train = {}
        self.y_val = {}
        for i in range(self.max_length):
            self.y_train[f'char_{i}'] = y_train_temp[:, i]
            self.y_val[f'char_{i}'] = y_val_temp[:, i]
        
        print(f"🏋️  Entrenamiento: {len(self.X_train)} muestras")
        print(f"✅ Validación: {len(self.X_val)} muestras")
    
    def create_model(self):
        """Crea el modelo CNN para captchas"""
        print("🏗️  Creando modelo...")
        
        # Input
        input_img = layers.Input(shape=(self.img_height, self.img_width, 1), name='image')
        
        # CNN layers
        x = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(input_img)
        x = layers.MaxPooling2D((2, 2))(x)
        
        x = layers.Conv2D(64, (3, 3), activation='relu', padding='same')(x)
        x = layers.MaxPooling2D((2, 2))(x)
        
        x = layers.Conv2D(128, (3, 3), activation='relu', padding='same')(x)
        x = layers.MaxPooling2D((2, 2))(x)
        
        x = layers.Conv2D(256, (3, 3), activation='relu', padding='same')(x)
        x = layers.MaxPooling2D((2, 2))(x)
        
        # Flatten para dense layers
        x = layers.Flatten()(x)
        x = layers.Dense(512, activation='relu')(x)
        x = layers.Dropout(0.5)(x)
        x = layers.Dense(256, activation='relu')(x)
        x = layers.Dropout(0.5)(x)

        # Output layers - una para cada posición del captcha
        outputs = []
        for i in range(self.max_length):
            output = layers.Dense(len(self.characters) + 1, activation='softmax', name=f'char_{i}')(x)
            outputs.append(output)
        
        self.model = keras.Model(inputs=input_img, outputs=outputs, name='captcha_model')
        
        print("✅ Modelo creado")
        self.model.summary()
    
    def compile_model(self):
        """Compila el modelo"""
        print("⚙️  Compilando modelo...")

        # Para múltiples salidas, necesitamos especificar la pérdida para cada una
        losses = {}
        metrics = {}
        for i in range(self.max_length):
            losses[f'char_{i}'] = 'sparse_categorical_crossentropy'
            metrics[f'char_{i}'] = 'accuracy'

        self.model.compile(
            optimizer='adam',
            loss=losses,
            metrics=metrics
        )

        print("✅ Modelo compilado")
    
    def train_model(self, epochs=50, batch_size=32):
        """Entrena el modelo"""
        print(f"🏋️  Iniciando entrenamiento ({epochs} épocas)...")
        
        # Callbacks
        callbacks = [
            keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
            keras.callbacks.ReduceLROnPlateau(factor=0.5, patience=5),
            keras.callbacks.ModelCheckpoint(
                os.path.join(self.model_dir, 'best_model.h5'),
                save_best_only=True
            )
        ]
        
        # Entrenar
        history = self.model.fit(
            self.X_train, self.y_train,
            batch_size=batch_size,
            epochs=epochs,
            validation_data=(self.X_val, self.y_val),
            callbacks=callbacks,
            verbose=1
        )
        
        print("✅ Entrenamiento completado")
        return history
    
    def evaluate_model(self):
        """Evalúa el modelo"""
        print("📊 Evaluando modelo...")
        
        # Predicciones en validación
        predictions = self.model.predict(self.X_val)

        correct = 0
        total = len(self.X_val)

        for i in range(total):
            # Decodificar predicción - ahora predictions es una lista de arrays
            pred_indices = []
            for j in range(self.max_length):
                pred_char = np.argmax(predictions[j][i])
                pred_indices.append(pred_char)
            pred_text = self.decode_prediction(pred_indices)

            # Etiqueta real
            true_indices = []
            for j in range(self.max_length):
                true_indices.append(self.y_val[f'char_{j}'][i])
            true_text = self.decode_prediction(true_indices)

            if pred_text.strip() == true_text.strip():
                correct += 1

            if i < 10:  # Mostrar primeros 10 ejemplos
                print(f"   Ejemplo {i+1}: '{true_text}' -> '{pred_text}' {'✅' if pred_text.strip() == true_text.strip() else '❌'}")
        
        accuracy = correct / total
        print(f"\n📈 Precisión en validación: {accuracy:.2%} ({correct}/{total})")
        
        return accuracy
    
    def save_model(self):
        """Guarda el modelo y metadatos"""
        print("💾 Guardando modelo...")
        
        # Guardar modelo
        model_path = os.path.join(self.model_dir, 'captcha_model.h5')
        self.model.save(model_path)
        
        # Guardar metadatos
        metadata = {
            'characters': self.characters,
            'char_to_num': self.char_to_num,
            'num_to_char': self.num_to_char,
            'img_width': self.img_width,
            'img_height': self.img_height,
            'max_length': self.max_length,
            'num_classes': len(self.characters) + 1
        }
        
        metadata_path = os.path.join(self.model_dir, 'metadata.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Modelo guardado en: {model_path}")
        print(f"✅ Metadatos guardados en: {metadata_path}")
    
    def train_complete_pipeline(self, epochs=50, batch_size=32):
        """Pipeline completo de entrenamiento"""
        print("🚀 INICIANDO ENTRENAMIENTO COMPLETO")
        print("=" * 60)
        
        try:
            # Preparar datos
            self.prepare_data()
            
            if len(self.images) < 50:
                print("❌ No hay suficientes datos para entrenar")
                return False
            
            # Crear y compilar modelo
            self.create_model()
            self.compile_model()
            
            # Entrenar
            history = self.train_model(epochs, batch_size)
            
            # Evaluar
            accuracy = self.evaluate_model()
            
            # Guardar
            self.save_model()
            
            print(f"\n🎉 ¡ENTRENAMIENTO COMPLETADO!")
            print(f"📈 Precisión final: {accuracy:.2%}")
            
            if accuracy > 0.8:
                print("🌟 ¡Excelente! El modelo tiene buena precisión")
            elif accuracy > 0.6:
                print("👍 Buen resultado. Considera recolectar más datos para mejorar")
            else:
                print("⚠️  Precisión baja. Necesitas más datos de entrenamiento")
            
            return True
            
        except Exception as e:
            print(f"❌ Error durante el entrenamiento: {e}")
            return False

def main():
    """Función principal"""
    print("🎯 ENTRENADOR DE MODELO DE CAPTCHAS SAT")
    print("=" * 50)
    
    trainer = CaptchaModelTrainer()
    
    if len(trainer.labels) < 50:
        print("❌ Necesitas al menos 50 imágenes etiquetadas para entrenar")
        print("💡 Usa captcha_data_collector.py para recolectar más datos")
        return
    
    print(f"📊 Dataset disponible: {len(trainer.labels)} imágenes etiquetadas")
    
    # Configuración de entrenamiento
    epochs = int(input("🔢 Número de épocas (recomendado: 50): ") or "50")
    batch_size = int(input("📦 Tamaño de lote (recomendado: 32): ") or "32")
    
    # Entrenar
    success = trainer.train_complete_pipeline(epochs, batch_size)
    
    if success:
        print("\n🎉 ¡Modelo entrenado exitosamente!")
        print("💡 Ahora puedes usar el modelo con captcha_predictor.py")
    else:
        print("\n❌ El entrenamiento falló")

if __name__ == "__main__":
    main()
