#!/usr/bin/env python3
"""
Etiquetado rápido con predicciones del modelo actual
"""

import os
import json
from predict_traditional import TraditionalCaptchaPredictor

def main():
    print("🏷️  ETIQUETADO RÁPIDO CON PREDICCIONES")
    
    # Cargar predictor
    try:
        predictor = TraditionalCaptchaPredictor()
    except:
        print("❌ No hay modelo entrenado. Usa: python train_captcha_traditional.py")
        return
    
    # Cargar etiquetas
    labels_file = "captcha_dataset/labels.json"
    with open(labels_file, 'r') as f:
        labels_data = json.load(f)
    
    # Encontrar imágenes sin etiquetar
    unlabeled = []
    for hash_id, data in labels_data.items():
        if not data.get('labeled', False):
            image_path = os.path.join('captcha_dataset/images', data['filename'])
            if os.path.exists(image_path):
                unlabeled.append((hash_id, data, image_path))
    
    if not unlabeled:
        print("✅ Todas las imágenes están etiquetadas!")
        return
    
    print(f"📊 {len(unlabeled)} imágenes sin etiquetar")
    print("💡 El modelo predecirá, tú confirmas o corriges")
    print("   Comandos: ENTER=aceptar, texto=corregir, 'skip'=saltar, 'quit'=salir")
    
    labeled_count = 0
    
    for i, (hash_id, data, image_path) in enumerate(unlabeled):
        print(f"\n📊 Progreso: {i+1}/{len(unlabeled)}")
        print(f"🖼️  Imagen: {data['filename']}")
        print(f"💡 Abre la imagen en: {image_path}")
        
        # Predecir con el modelo
        try:
            # Intentar diferentes longitudes
            predictions = {}
            for length in [2, 3, 4, 5, 6]:
                pred = predictor.predict_captcha(image_path, length)
                if pred and len(pred) == length:
                    predictions[length] = pred
            
            if predictions:
                # Mostrar predicciones
                print("🤖 Predicciones del modelo:")
                for length, pred in predictions.items():
                    print(f"   {length} chars: {pred}")
                
                # Usar la predicción más probable (longitud 5 es más común)
                best_pred = predictions.get(5) or list(predictions.values())[0]
                print(f"🎯 Mejor predicción: {best_pred}")
            else:
                best_pred = ""
                print("❓ No se pudo predecir")
        except:
            best_pred = ""
            print("❓ Error en predicción")
        
        # Pedir confirmación/corrección
        if best_pred:
            user_input = input(f"✏️  Confirma '{best_pred}' o ingresa corrección (ENTER=aceptar, skip, quit): ").strip()
        else:
            user_input = input(f"✏️  Ingresa el texto del captcha (skip, quit): ").strip()
        
        if user_input.lower() == 'quit':
            break
        elif user_input.lower() == 'skip':
            continue
        elif user_input == '' and best_pred:
            # Aceptar predicción
            final_answer = best_pred
        elif user_input != '':
            # Usar corrección del usuario
            final_answer = user_input
        else:
            continue
        
        # Validar respuesta
        if len(final_answer) >= 1 and final_answer.replace(' ', '').isalnum():
            # Guardar etiqueta
            labels_data[hash_id]['answer'] = final_answer
            labels_data[hash_id]['labeled'] = True
            labeled_count += 1
            print(f"✅ Etiquetado: '{final_answer}'")
        else:
            print("❌ Respuesta inválida")
    
    # Guardar cambios
    if labeled_count > 0:
        with open(labels_file, 'w') as f:
            json.dump(labels_data, f, indent=2)
        print(f"\n✅ {labeled_count} nuevas etiquetas guardadas")
        
        # Sugerir re-entrenamiento
        total_labeled = sum(1 for data in labels_data.values() if data.get('labeled', False))
        print(f"📊 Total etiquetado: {total_labeled}")
        
        if total_labeled >= 350:
            print(f"🚀 ¡Suficientes datos! Puedes re-entrenar:")
            print(f"   python auto_retrain.py")
    else:
        print("❌ No se etiquetaron nuevas imágenes")

if __name__ == "__main__":
    main()
