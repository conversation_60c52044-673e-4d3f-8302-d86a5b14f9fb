#!/usr/bin/env python3
"""
Predictor tradicional de captchas SAT
"""

import os
import json
import numpy as np
import cv2
import pickle
import argparse
import sys

class TraditionalCaptchaPredictor:
    """Predictor tradicional para captchas SAT"""
    
    def __init__(self, model_dir="trained_model"):
        self.model_dir = model_dir
        self.model_path = os.path.join(model_dir, "traditional_model.pkl")
        self.metadata_path = os.path.join(model_dir, "traditional_metadata.json")
        
        self.load_model()
        
    def load_model(self):
        """Carga el modelo entrenado"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"Modelo no encontrado: {self.model_path}")
        
        with open(self.model_path, 'rb') as f:
            self.model = pickle.load(f)
        
        with open(self.metadata_path, 'r') as f:
            metadata = json.load(f)
        
        self.characters = metadata['characters']
        self.char_to_num = metadata['char_to_num']
        self.num_to_char = {int(k): v for k, v in metadata['num_to_char'].items()}
        
        print(f"✅ Modelo tradicional cargado")
        
    def segment_characters(self, image, num_chars):
        """Segmenta caracteres dividiendo en partes iguales"""
        _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        height, width = binary.shape
        char_width = width // num_chars
        
        characters = []
        for i in range(num_chars):
            start_x = i * char_width
            end_x = start_x + char_width
            
            char_region = binary[:, start_x:end_x]
            
            horizontal_proj = np.sum(char_region, axis=1)
            non_zero_rows = np.where(horizontal_proj > 0)[0]
            
            if len(non_zero_rows) > 0:
                top = non_zero_rows[0]
                bottom = non_zero_rows[-1] + 1
                char_img = char_region[top:bottom, :]
            else:
                char_img = char_region
            
            if char_img.size > 0:
                char_img = cv2.resize(char_img, (20, 30))
                characters.append(char_img)
        
        return characters
    
    def extract_features(self, char_image):
        """Extrae características simples"""
        features = char_image.flatten()
        density = np.sum(char_image > 0) / char_image.size
        h_proj = np.sum(char_image, axis=1)
        v_proj = np.sum(char_image, axis=0)
        
        all_features = np.concatenate([
            features,
            [density],
            h_proj,
            v_proj
        ])
        
        return all_features
    
    def predict_captcha(self, image_path, expected_length=5):
        """Predice un captcha completo"""
        image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if image is None:
            return None
            
        characters = self.segment_characters(image, expected_length)
        
        result = ""
        for char_img in characters:
            features = self.extract_features(char_img)
            pred = self.model.predict([features])[0]
            result += self.num_to_char[pred]
        
        return result

def main():
    parser = argparse.ArgumentParser(description='Predictor tradicional de captchas SAT')
    parser.add_argument('image_path', help='Ruta de la imagen del captcha')
    parser.add_argument('--length', type=int, default=5, help='Longitud esperada del captcha')
    parser.add_argument('--verbose', action='store_true', help='Mostrar información detallada')
    
    args = parser.parse_args()
    
    try:
        predictor = TraditionalCaptchaPredictor()
        result = predictor.predict_captcha(args.image_path, args.length)
        
        if result:
            print(f"🎯 Predicción: {result}")
        else:
            print("❌ No se pudo procesar la imagen")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
