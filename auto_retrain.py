#!/usr/bin/env python3
"""
Re-entrenamiento automático cuando hay más datos
"""

import os
import json
from train_captcha_traditional import TraditionalCaptchaTrainer
from evaluate_traditional import main as evaluate_model

def check_data_count():
    """Verifica cuántos datos etiquetados hay"""
    labels_file = "captcha_dataset/labels.json"
    
    if not os.path.exists(labels_file):
        return 0
    
    with open(labels_file, 'r') as f:
        labels_data = json.load(f)
    
    labeled_count = sum(1 for data in labels_data.values() if data.get('labeled', False))
    return labeled_count

def main():
    print("🔄 AUTO RE-ENTRENAMIENTO")
    
    current_count = check_data_count()
    print(f"📊 Datos etiquetados actuales: {current_count}")
    
    if current_count < 350:
        print(f"⏳ Necesitas al menos 350 datos etiquetados para re-entrenar")
        print(f"   Faltan: {350 - current_count} datos")
        print(f"   Usa: python captcha_data_collector.py")
        return
    
    print(f"🚀 Suficientes datos para re-entrenar!")
    
    # Entrenar modelo
    print(f"\n🏋️  ENTRENANDO MODELO...")
    trainer = TraditionalCaptchaTrainer()
    trainer.load_and_process_data()
    accuracy = trainer.train_model()
    
    print(f"\n📈 Precisión por carácter: {accuracy:.1%}")
    
    # Evaluar modelo completo
    print(f"\n🔍 EVALUANDO MODELO COMPLETO...")
    evaluate_model()
    
    print(f"\n✅ Re-entrenamiento completado!")
    print(f"   Usa: python predict_traditional.py imagen.png")

if __name__ == "__main__":
    main()
