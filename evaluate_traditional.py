#!/usr/bin/env python3
"""
Evaluador del modelo tradicional
"""

import os
import json
from predict_traditional import TraditionalCaptchaPredictor

def main():
    print("🔍 EVALUANDO MODELO TRADICIONAL")
    
    predictor = TraditionalCaptchaPredictor()
    
    # Cargar etiquetas
    with open('captcha_dataset/labels.json', 'r') as f:
        labels_data = json.load(f)
    
    correct = 0
    total = 0
    examples = []
    
    for hash_id, data in labels_data.items():
        if not data.get('labeled', False):
            continue
            
        image_path = os.path.join('captcha_dataset/images', data['filename'])
        if not os.path.exists(image_path):
            continue
            
        true_answer = data['answer'].strip()
        predicted = predictor.predict_captcha(image_path, len(true_answer))
        
        if predicted == true_answer:
            correct += 1
            status = "✅"
        else:
            status = "❌"
            
        total += 1
        
        if len(examples) < 20:  # Mostrar primeros 20 ejemplos
            examples.append(f"   {status} '{true_answer}' -> '{predicted}'")
    
    accuracy = correct / total if total > 0 else 0
    
    print(f"\n📊 RESULTADOS:")
    print(f"   Total evaluado: {total}")
    print(f"   Correctos: {correct}")
    print(f"   Precisión: {accuracy:.1%}")
    
    print(f"\n📝 EJEMPLOS:")
    for example in examples:
        print(example)
    
    if accuracy > 0.7:
        print(f"\n🎯 ¡Excelente! Precisión muy buena")
    elif accuracy > 0.5:
        print(f"\n⚠️  Precisión moderada - funcional")
    elif accuracy > 0.3:
        print(f"\n🔧 Precisión baja - necesita mejoras")
    else:
        print(f"\n❌ Precisión muy baja")

if __name__ == "__main__":
    main()
