#!/usr/bin/env python3
"""
Entrenador tradicional con segmentación de caracteres
"""

import os
import json
import numpy as np
import cv2
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score
import pickle

class TraditionalCaptchaTrainer:
    """Entrenador tradicional con segmentación"""
    
    def __init__(self):
        self.dataset_dir = "captcha_dataset"
        self.images_dir = os.path.join(self.dataset_dir, "images")
        self.labels_file = os.path.join(self.dataset_dir, "labels.json")
        self.model_dir = "trained_model"
        
        self.characters = '0123456789'
        self.char_to_num = {char: idx for idx, char in enumerate(self.characters)}
        self.num_to_char = {idx: char for idx, char in enumerate(self.characters)}
        
    def segment_characters(self, image, num_chars):
        """Segmenta caracteres dividiendo en partes iguales"""
        # Binarizar imagen
        _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        height, width = binary.shape
        char_width = width // num_chars

        characters = []
        for i in range(num_chars):
            start_x = i * char_width
            end_x = start_x + char_width

            # Extraer región del carácter
            char_region = binary[:, start_x:end_x]

            # Encontrar límites verticales reales del contenido
            horizontal_proj = np.sum(char_region, axis=1)
            non_zero_rows = np.where(horizontal_proj > 0)[0]

            if len(non_zero_rows) > 0:
                top = non_zero_rows[0]
                bottom = non_zero_rows[-1] + 1
                char_img = char_region[top:bottom, :]
            else:
                char_img = char_region

            # Redimensionar
            if char_img.size > 0:
                char_img = cv2.resize(char_img, (20, 30))
                characters.append(char_img)

        return characters
    
    def extract_features(self, char_image):
        """Extrae características simples"""
        # Aplanar imagen
        features = char_image.flatten()
        
        # Densidad de píxeles
        density = np.sum(char_image > 0) / char_image.size
        
        # Proyecciones
        h_proj = np.sum(char_image, axis=1)
        v_proj = np.sum(char_image, axis=0)
        
        # Combinar características
        all_features = np.concatenate([
            features,
            [density],
            h_proj,
            v_proj
        ])
        
        return all_features
        
    def load_and_process_data(self):
        """Carga y procesa datos"""
        print("🔄 Procesando datos...")

        with open(self.labels_file, 'r') as f:
            labels_data = json.load(f)

        all_features = []
        all_labels = []
        processed = 0

        for hash_id, data in labels_data.items():
            if not data.get('labeled', False):
                continue

            image_path = os.path.join(self.images_dir, data['filename'])
            if not os.path.exists(image_path):
                continue

            image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            if image is None:
                continue

            answer = data['answer'].strip()
            characters = self.segment_characters(image, len(answer))

            processed += 1
            if processed <= 5:  # Debug primeras 5 imágenes
                print(f"   Imagen {processed}: '{answer}' -> {len(characters)} caracteres segmentados")

            for char_img, char_label in zip(characters, answer):
                if char_label in self.char_to_num:
                    features = self.extract_features(char_img)
                    all_features.append(features)
                    all_labels.append(self.char_to_num[char_label])

        self.features = np.array(all_features)
        self.labels = np.array(all_labels)

        print(f"✅ {len(self.features)} caracteres procesados de {processed} imágenes")
        
    def train_model(self):
        """Entrena modelo Random Forest"""
        print("🏋️  Entrenando...")
        
        # Dividir datos
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            self.features, self.labels, test_size=0.2, random_state=42
        )
        
        # Entrenar Random Forest
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.model.fit(X_train, y_train)
        
        # Evaluar
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"📈 Precisión por carácter: {accuracy:.1%}")
        
        # Guardar modelo
        os.makedirs(self.model_dir, exist_ok=True)
        with open(os.path.join(self.model_dir, 'traditional_model.pkl'), 'wb') as f:
            pickle.dump(self.model, f)
        
        # Guardar metadatos
        metadata = {
            'characters': self.characters,
            'char_to_num': self.char_to_num,
            'num_to_char': self.num_to_char
        }
        with open(os.path.join(self.model_dir, 'traditional_metadata.json'), 'w') as f:
            json.dump(metadata, f)
        
        return accuracy
    
    def predict_captcha(self, image_path):
        """Predice un captcha completo"""
        image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        characters = self.segment_characters(image)
        
        result = ""
        for char_img in characters:
            features = self.extract_features(char_img)
            pred = self.model.predict([features])[0]
            result += self.num_to_char[pred]
        
        return result

def main():
    print("🚀 ENTRENADOR TRADICIONAL")
    
    trainer = TraditionalCaptchaTrainer()
    trainer.load_and_process_data()
    accuracy = trainer.train_model()
    
    if accuracy > 0.7:
        print("🎯 ¡Excelente!")
    elif accuracy > 0.5:
        print("⚠️  Moderado")
    else:
        print("❌ Necesita mejoras")

if __name__ == "__main__":
    main()
