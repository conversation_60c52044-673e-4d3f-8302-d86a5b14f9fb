#!/usr/bin/env python3
"""
Entrenador de captchas SAT - Versión simplificada y optimizada
"""

import os
import json
import numpy as np
import cv2
from sklearn.model_selection import train_test_split
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import matplotlib.pyplot as plt
from collections import Counter

class SimpleCaptchaTrainer:
    """Entrenador simplificado para captchas SAT"""
    
    def __init__(self):
        """Inicializa el entrenador"""
        self.dataset_dir = "captcha_dataset"
        self.images_dir = os.path.join(self.dataset_dir, "images")
        self.labels_file = os.path.join(self.dataset_dir, "labels.json")
        self.model_dir = "trained_model"
        
        # Parámetros de imagen
        self.img_width = 160
        self.img_height = 60
        
        # Solo dígitos 0-9 + padding
        self.characters = '0123456789'
        self.max_length = 5  # Máximo 5 caracteres
        
        # Mapeos
        self.char_to_num = {char: idx for idx, char in enumerate(self.characters)}
        self.num_to_char = {idx: char for idx, char in enumerate(self.characters)}
        
        print(f"🎯 Caracteres: {self.characters}")
        print(f"📏 Longitud máxima: {self.max_length}")
        
    def load_and_preprocess_data(self):
        """Carga y preprocesa los datos"""
        print("🔄 Cargando datos...")
        
        # Cargar etiquetas
        with open(self.labels_file, 'r') as f:
            labels_data = json.load(f)
        
        images = []
        labels = []
        
        for hash_id, data in labels_data.items():
            if not data.get('labeled', False):
                continue
                
            # Cargar imagen
            image_path = os.path.join(self.images_dir, data['filename'])
            if not os.path.exists(image_path):
                continue
                
            # Preprocesar imagen
            image = self.preprocess_image(image_path)
            if image is None:
                continue
                
            # Preprocesar etiqueta
            label = self.encode_label(data['answer'])
            if label is None:
                continue
                
            images.append(image)
            labels.append(label)
        
        if len(images) == 0:
            raise ValueError("No se encontraron imágenes válidas")
            
        self.images = np.array(images)
        self.labels = np.array(labels)
        
        print(f"✅ Datos cargados: {len(self.images)} muestras")
        print(f"📏 Forma de imágenes: {self.images.shape}")
        print(f"📏 Forma de etiquetas: {self.labels.shape}")
        
        # Dividir datos
        self.X_train, self.X_val, self.y_train, self.y_val = train_test_split(
            self.images, self.labels, test_size=0.2, random_state=42
        )
        
        print(f"🏋️  Entrenamiento: {len(self.X_train)} muestras")
        print(f"✅ Validación: {len(self.X_val)} muestras")
        
    def preprocess_image(self, image_path):
        """Preprocesa una imagen"""
        try:
            # Cargar imagen
            image = cv2.imread(image_path)
            if image is None:
                return None
                
            # Convertir a escala de grises
            if len(image.shape) == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Redimensionar
            image = cv2.resize(image, (self.img_width, self.img_height))
            
            # Normalizar
            image = image.astype(np.float32) / 255.0
            
            # Agregar dimensión de canal
            image = np.expand_dims(image, axis=-1)
            
            return image
            
        except Exception as e:
            print(f"❌ Error procesando {image_path}: {e}")
            return None
    
    def encode_label(self, text):
        """Codifica una etiqueta de texto"""
        try:
            # Limpiar texto
            text = text.strip().upper()
            
            # Verificar que solo contenga dígitos
            if not text.isdigit():
                return None
                
            # Verificar longitud
            if len(text) > self.max_length:
                return None
                
            # Convertir a array de índices
            label = []
            for char in text:
                if char in self.char_to_num:
                    label.append(self.char_to_num[char])
                else:
                    return None
            
            # Padding con -1 (será ignorado en la pérdida)
            while len(label) < self.max_length:
                label.append(-1)
                
            return np.array(label)
            
        except Exception as e:
            print(f"❌ Error codificando '{text}': {e}")
            return None
    
    def create_model(self):
        """Crea un modelo CNN simple"""
        print("🏗️  Creando modelo simplificado...")
        
        # Input
        inputs = layers.Input(shape=(self.img_height, self.img_width, 1), name='image')
        
        # CNN Feature Extraction
        x = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(inputs)
        x = layers.MaxPooling2D((2, 2))(x)
        x = layers.BatchNormalization()(x)
        
        x = layers.Conv2D(64, (3, 3), activation='relu', padding='same')(x)
        x = layers.MaxPooling2D((2, 2))(x)
        x = layers.BatchNormalization()(x)
        
        x = layers.Conv2D(128, (3, 3), activation='relu', padding='same')(x)
        x = layers.MaxPooling2D((2, 2))(x)
        x = layers.BatchNormalization()(x)
        
        # Global Average Pooling instead of Flatten
        x = layers.GlobalAveragePooling2D()(x)
        
        # Dense layers
        x = layers.Dense(256, activation='relu')(x)
        x = layers.Dropout(0.5)(x)
        x = layers.Dense(128, activation='relu')(x)
        x = layers.Dropout(0.3)(x)
        
        # Output layers - una para cada posición
        outputs = []
        for i in range(self.max_length):
            output = layers.Dense(len(self.characters), activation='softmax', name=f'digit_{i}')(x)
            outputs.append(output)
        
        self.model = keras.Model(inputs=inputs, outputs=outputs, name='simple_captcha_model')
        
        print("✅ Modelo creado")
        self.model.summary()
        
    def compile_model(self):
        """Compila el modelo"""
        print("⚙️  Compilando modelo...")
        
        # Función de pérdida personalizada que ignora padding (-1)
        def masked_sparse_categorical_crossentropy(y_true, y_pred):
            # Convertir y_true a int32
            y_true = tf.cast(y_true, tf.int32)

            # Crear máscara para ignorar valores -1
            mask = tf.cast(tf.not_equal(y_true, -1), tf.float32)

            # Reemplazar -1 con 0 para evitar errores
            y_true_masked = tf.where(tf.equal(y_true, -1), 0, y_true)

            # Calcular pérdida
            loss = tf.keras.losses.sparse_categorical_crossentropy(y_true_masked, y_pred)

            # Aplicar máscara
            loss = loss * mask

            # Promedio solo sobre elementos válidos
            return tf.reduce_sum(loss) / tf.maximum(tf.reduce_sum(mask), 1.0)
        
        # Configurar pérdidas y métricas
        losses = {}
        metrics = {}
        for i in range(self.max_length):
            losses[f'digit_{i}'] = masked_sparse_categorical_crossentropy
            metrics[f'digit_{i}'] = 'accuracy'
        
        self.model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss=losses,
            metrics=metrics
        )
        
        print("✅ Modelo compilado")
        
    def prepare_labels_for_training(self):
        """Prepara las etiquetas para entrenamiento multi-output"""
        y_train_dict = {}
        y_val_dict = {}
        
        for i in range(self.max_length):
            y_train_dict[f'digit_{i}'] = self.y_train[:, i]
            y_val_dict[f'digit_{i}'] = self.y_val[:, i]
        
        return y_train_dict, y_val_dict
        
    def train_model(self, epochs=30, batch_size=16):
        """Entrena el modelo"""
        print(f"🏋️  Iniciando entrenamiento ({epochs} épocas, batch_size={batch_size})...")
        
        # Preparar etiquetas
        y_train_dict, y_val_dict = self.prepare_labels_for_training()
        
        # Callbacks
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-6
            )
        ]
        
        # Entrenar
        history = self.model.fit(
            self.X_train, y_train_dict,
            validation_data=(self.X_val, y_val_dict),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        print("✅ Entrenamiento completado")
        return history
        
    def evaluate_model(self):
        """Evalúa el modelo"""
        print("📊 Evaluando modelo...")
        
        predictions = self.model.predict(self.X_val, verbose=0)
        
        correct = 0
        total = len(self.X_val)
        
        for i in range(total):
            # Decodificar predicción
            pred_text = self.decode_prediction([pred[i] for pred in predictions])
            
            # Etiqueta real
            true_text = self.decode_label(self.y_val[i])
            
            if pred_text == true_text:
                correct += 1
            
            if i < 10:  # Mostrar primeros 10 ejemplos
                print(f"   Ejemplo {i+1}: '{true_text}' -> '{pred_text}' {'✅' if pred_text == true_text else '❌'}")
        
        accuracy = correct / total
        print(f"\n📈 Precisión en validación: {accuracy:.1%} ({correct}/{total})")
        
        return accuracy
        
    def decode_prediction(self, prediction_list):
        """Decodifica una predicción a texto"""
        decoded = []
        for pred in prediction_list:
            digit_idx = np.argmax(pred)
            if digit_idx < len(self.characters):
                decoded.append(self.characters[digit_idx])
        
        return ''.join(decoded).rstrip('0')  # Remover padding de ceros al final
        
    def decode_label(self, label_array):
        """Decodifica una etiqueta a texto"""
        decoded = []
        for idx in label_array:
            if idx >= 0 and idx < len(self.characters):
                decoded.append(self.characters[idx])
        
        return ''.join(decoded)
        
    def save_model(self):
        """Guarda el modelo y metadatos"""
        print("💾 Guardando modelo...")
        
        # Crear directorio
        os.makedirs(self.model_dir, exist_ok=True)
        
        # Guardar modelo
        model_path = os.path.join(self.model_dir, 'captcha_model.h5')
        self.model.save(model_path)
        
        # Guardar metadatos
        metadata = {
            'characters': self.characters,
            'max_length': self.max_length,
            'img_width': self.img_width,
            'img_height': self.img_height,
            'char_to_num': self.char_to_num,
            'num_to_char': self.num_to_char
        }
        
        metadata_path = os.path.join(self.model_dir, 'metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"✅ Modelo guardado en: {model_path}")
        print(f"✅ Metadatos guardados en: {metadata_path}")

def main():
    """Función principal"""
    print("🚀 ENTRENADOR SIMPLIFICADO DE CAPTCHAS SAT")
    print("=" * 60)
    
    try:
        # Crear entrenador
        trainer = SimpleCaptchaTrainer()
        
        # Cargar datos
        trainer.load_and_preprocess_data()
        
        # Crear modelo
        trainer.create_model()
        trainer.compile_model()
        
        # Entrenar
        history = trainer.train_model(epochs=50, batch_size=16)
        
        # Evaluar
        accuracy = trainer.evaluate_model()
        
        # Guardar
        trainer.save_model()
        
        print(f"\n🎉 ¡ENTRENAMIENTO COMPLETADO!")
        print(f"📈 Precisión final: {accuracy:.1%}")
        
        if accuracy > 0.5:
            print("🎯 ¡Excelente! El modelo está funcionando bien.")
        elif accuracy > 0.1:
            print("⚠️  Precisión moderada. Considera recolectar más datos.")
        else:
            print("❌ Precisión baja. Revisa los datos y la arquitectura.")
            
    except Exception as e:
        print(f"❌ Error durante el entrenamiento: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
