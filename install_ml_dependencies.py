#!/usr/bin/env python3
"""
Instalador de dependencias para entrenamiento de modelos de captcha
"""

import subprocess
import sys
import os

def install_package(package):
    """Instala un paquete usando pip"""
    try:
        print(f"📦 Instalando {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} instalado correctamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando {package}: {e}")
        return False

def check_gpu_support():
    """Verifica soporte para GPU"""
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"🚀 GPU detectada: {len(gpus)} dispositivo(s)")
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu.name}")
            return True
        else:
            print("💻 Solo CPU disponible (entrenamiento será más lento)")
            return False
    except ImportError:
        print("⚠️  TensorFlow no instalado aún")
        return False

def main():
    """Función principal"""
    print("🎯 INSTALADOR DE DEPENDENCIAS PARA ML")
    print("=" * 50)
    
    # Lista de paquetes necesarios
    packages = [
        "tensorflow>=2.10.0",
        "opencv-python",
        "pillow",
        "numpy",
        "matplotlib",
        "scikit-learn",
        "requests",
        "selenium"
    ]
    
    print("📋 Paquetes a instalar:")
    for package in packages:
        print(f"   • {package}")
    
    print("\n🚀 Iniciando instalación...")
    
    successful = 0
    failed = 0
    
    for package in packages:
        if install_package(package):
            successful += 1
        else:
            failed += 1
        print()
    
    print("=" * 50)
    print("📊 RESUMEN DE INSTALACIÓN:")
    print(f"   ✅ Exitosos: {successful}")
    print(f"   ❌ Fallidos: {failed}")
    
    if failed == 0:
        print("\n🎉 ¡Todas las dependencias instaladas correctamente!")
        
        # Verificar GPU
        print("\n🔍 Verificando soporte GPU...")
        check_gpu_support()
        
        print("\n💡 PRÓXIMOS PASOS:")
        print("1. Ejecuta: python captcha_data_collector.py")
        print("2. Recolecta al menos 200-500 captchas etiquetados")
        print("3. Ejecuta: python train_captcha_model.py")
        print("4. Usa: python captcha_predictor.py imagen.png")
        
    else:
        print(f"\n⚠️  {failed} paquetes fallaron. Revisa los errores arriba.")
        print("💡 Intenta ejecutar el script como administrador")

if __name__ == "__main__":
    main()
