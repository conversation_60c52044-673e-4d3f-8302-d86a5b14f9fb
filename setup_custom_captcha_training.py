#!/usr/bin/env python3
"""
Configurador automático para entrenamiento de captchas personalizado
"""

import os
import subprocess
import sys

def print_header(title):
    """Imprime un encabezado formateado"""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)

def check_dependencies():
    """Verifica las dependencias necesarias"""
    print("🔍 Verificando dependencias...")
    
    required_packages = [
        'tensorflow',
        'opencv-python', 
        'pillow',
        'numpy',
        'matplotlib',
        'scikit-learn',
        'selenium'
    ]
    
    missing = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package}")
            missing.append(package)
    
    return missing

def install_dependencies(missing_packages):
    """Instala las dependencias faltantes"""
    if not missing_packages:
        return True
    
    print(f"\n📦 Instalando {len(missing_packages)} paquetes faltantes...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install"
        ] + missing_packages)
        print("✅ Dependencias instaladas correctamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False

def create_directory_structure():
    """Crea la estructura de directorios necesaria"""
    print("📁 Creando estructura de directorios...")
    
    directories = [
        "captcha_dataset",
        "captcha_dataset/images",
        "trained_model"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   ✅ {directory}")

def show_training_guide():
    """Muestra la guía de entrenamiento"""
    print_header("GUÍA DE ENTRENAMIENTO PASO A PASO")
    
    steps = [
        ("1. 📸 RECOLECTAR DATOS", [
            "python captcha_data_collector.py",
            "• Selecciona opción 1 (etiquetado manual)",
            "• Recolecta mínimo 200 captchas (ideal: 500+)",
            "• Cada captcha necesita ser etiquetado manualmente"
        ]),
        ("2. 🏋️ ENTRENAR MODELO", [
            "python train_captcha_model.py",
            "• El entrenamiento toma 30-60 minutos",
            "• Con GPU será mucho más rápido",
            "• Objetivo: >80% de precisión"
        ]),
        ("3. 🧪 PROBAR MODELO", [
            "python captcha_predictor.py --test",
            "python captcha_predictor.py imagen.png",
            "• Verifica que el modelo funcione bien",
            "• Debe resolver captchas con >90% precisión"
        ]),
        ("4. 🚀 INTEGRAR EN SISTEMA", [
            "El modelo se integra automáticamente",
            "• Prioridad: GLM-4.5V > Modelo personalizado > PHP > CLI",
            "• Tu modelo será el método #2 en la jerarquía"
        ])
    ]
    
    for step_title, step_details in steps:
        print(f"\n{step_title}")
        print("-" * 40)
        for detail in step_details:
            if detail.startswith("python"):
                print(f"   💻 {detail}")
            else:
                print(f"   {detail}")

def show_tips():
    """Muestra consejos importantes"""
    print_header("CONSEJOS IMPORTANTES")
    
    tips = [
        "🎯 CALIDAD DE DATOS:",
        "   • Más datos = mejor modelo",
        "   • Etiquetas correctas son críticas",
        "   • Diversidad de captchas mejora generalización",
        "",
        "⚡ RENDIMIENTO:",
        "   • GPU acelera entrenamiento 10-50x",
        "   • CPU funciona pero es más lento",
        "   • 8GB+ RAM recomendado",
        "",
        "🎨 OPTIMIZACIÓN:",
        "   • Si precisión <80%, recolecta más datos",
        "   • Si overfitting, reduce épocas",
        "   • Experimenta con diferentes arquitecturas",
        "",
        "🔧 INTEGRACIÓN:",
        "   • El modelo se guarda en 'trained_model/'",
        "   • Se integra automáticamente en sat_verifier.py",
        "   • Funciona como respaldo si GLM-4.5V falla"
    ]
    
    for tip in tips:
        print(tip)

def main():
    """Función principal"""
    print_header("CONFIGURADOR DE ENTRENAMIENTO DE CAPTCHAS")
    
    print("🎯 Este script te ayudará a configurar el entrenamiento")
    print("   de tu propio modelo de captchas personalizado para SAT")
    
    # Verificar dependencias
    print_header("VERIFICACIÓN DE DEPENDENCIAS")
    missing = check_dependencies()
    
    if missing:
        print(f"\n⚠️  Faltan {len(missing)} dependencias")
        install = input("¿Instalar automáticamente? (s/n): ").lower().strip()
        
        if install == 's':
            if not install_dependencies(missing):
                print("❌ No se pudieron instalar las dependencias")
                return
        else:
            print("💡 Instala manualmente: pip install " + " ".join(missing))
            return
    else:
        print("\n✅ Todas las dependencias están instaladas")
    
    # Crear estructura
    print_header("CONFIGURACIÓN DE DIRECTORIOS")
    create_directory_structure()
    
    # Mostrar guía
    show_training_guide()
    show_tips()
    
    print_header("¡CONFIGURACIÓN COMPLETADA!")
    print("🚀 Ya puedes empezar el entrenamiento:")
    print("   python captcha_data_collector.py")
    print("\n💡 Lee la guía arriba para el proceso completo")

if __name__ == "__main__":
    main()
